import EventBus from './EventBus.js';

/**
 * 页面管理器 - 负责页面检测和分发
 */
class PageManager {
  constructor() {
    this.pages = new Map();
    this.currentPage = null;
    this.eventBus = EventBus;
    this.isInitialized = false;
  }

  /**
   * 注册页面处理器
   * @param {Object} pattern 页面匹配模式
   * @param {Class} PageClass 页面处理器类
   */
  registerPage(pattern, PageClass) {
    const patternKey = this.generatePatternKey(pattern);
    this.pages.set(patternKey, {
      pattern,
      PageClass,
      instance: null
    });
    
    console.log(`📝 注册页面处理器: ${PageClass.name}`, pattern);
  }

  /**
   * 生成模式键
   * @param {Object} pattern 匹配模式
   * @returns {string}
   */
  generatePatternKey(pattern) {
    const parts = [];
    if (pattern.domain) parts.push(`domain:${pattern.domain}`);
    if (pattern.path) parts.push(`path:${pattern.path}`);
    if (pattern.params) {
      Object.entries(pattern.params).forEach(([key, value]) => {
        parts.push(`param:${key}=${value}`);
      });
    }
    return parts.join('|');
  }

  /**
   * 检查URL是否匹配模式
   * @param {string} url 当前URL
   * @param {Object} pattern 匹配模式
   * @returns {boolean}
   */
  matchUrl(url, pattern) {
    const urlObj = new URL(url);
    
    // 检查域名
    if (pattern.domain) {
      if (!urlObj.hostname.includes(pattern.domain)) {
        return false;
      }
    }
    
    // 检查路径
    if (pattern.path) {
      if (!urlObj.pathname.includes(pattern.path)) {
        return false;
      }
    }
    
    // 检查查询参数
    if (pattern.params) {
      const urlParams = new URLSearchParams(urlObj.search);
      for (const [key, value] of Object.entries(pattern.params)) {
        if (urlParams.get(key) !== value) {
          return false;
        }
      }
    }
    
    // 检查自定义匹配函数
    if (pattern.customMatcher && typeof pattern.customMatcher === 'function') {
      return pattern.customMatcher(url, urlObj);
    }
    
    return true;
  }

  /**
   * 查找匹配的页面处理器
   * @param {string} url 当前URL
   * @returns {Object|null}
   */
  findMatchingPage(url) {
    for (const [patternKey, pageInfo] of this.pages) {
      if (this.matchUrl(url, pageInfo.pattern)) {
        console.log(`🎯 找到匹配的页面处理器: ${pageInfo.PageClass.name}`);
        return pageInfo;
      }
    }
    return null;
  }

  /**
   * 初始化页面管理器
   */
  async initialize() {
    if (this.isInitialized) {
      console.warn('⚠️ PageManager 已经初始化过了');
      return;
    }

    console.log('🚀 PageManager 开始初始化...');
    
    // 监听URL变化
    this.setupUrlChangeListener();
    
    // 初始化当前页面
    await this.initializePage();
    
    this.isInitialized = true;
    console.log('✅ PageManager 初始化完成');
    
    // 触发初始化完成事件
    this.eventBus.emit('pageManager:initialized', {
      currentUrl: window.location.href,
      activePage: this.currentPage?.constructor.name || null
    });
  }

  /**
   * 设置URL变化监听器
   */
  setupUrlChangeListener() {
    // 监听popstate事件（浏览器前进后退）
    window.addEventListener('popstate', () => {
      console.log('🔄 检测到URL变化 (popstate)');
      this.handleUrlChange();
    });

    // 监听pushstate和replacestate（SPA路由变化）
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;
    
    history.pushState = function(...args) {
      originalPushState.apply(history, args);
      console.log('🔄 检测到URL变化 (pushState)');
      setTimeout(() => this.handleUrlChange(), 100);
    }.bind(this);
    
    history.replaceState = function(...args) {
      originalReplaceState.apply(history, args);
      console.log('🔄 检测到URL变化 (replaceState)');
      setTimeout(() => this.handleUrlChange(), 100);
    }.bind(this);
  }

  /**
   * 处理URL变化
   */
  async handleUrlChange() {
    const currentUrl = window.location.href;
    console.log('🔍 处理URL变化:', currentUrl);
    
    // 清理当前页面
    if (this.currentPage) {
      this.currentPage.cleanup();
      this.currentPage = null;
    }
    
    // 初始化新页面
    await this.initializePage();
  }

  /**
   * 初始化当前页面
   */
  async initializePage() {
    const currentUrl = window.location.href;
    console.log('🔍 检查当前页面:', currentUrl);
    
    const pageInfo = this.findMatchingPage(currentUrl);
    
    if (!pageInfo) {
      console.log('❌ 没有找到匹配的页面处理器');
      this.eventBus.emit('pageManager:noMatch', { url: currentUrl });
      return;
    }
    
    try {
      // 创建页面实例
      if (!pageInfo.instance) {
        pageInfo.instance = new pageInfo.PageClass();
      }
      
      // 初始化页面
      const success = await pageInfo.instance.initialize();
      
      if (success) {
        this.currentPage = pageInfo.instance;
        console.log(`✅ 页面处理器激活: ${pageInfo.PageClass.name}`);
        
        this.eventBus.emit('pageManager:pageActivated', {
          page: pageInfo.PageClass.name,
          url: currentUrl
        });
      } else {
        console.log(`❌ 页面处理器初始化失败: ${pageInfo.PageClass.name}`);
      }
    } catch (error) {
      console.error(`❌ 页面处理器初始化异常: ${pageInfo.PageClass.name}`, error);
    }
  }

  /**
   * 获取当前活动页面
   * @returns {PageBase|null}
   */
  getCurrentPage() {
    return this.currentPage;
  }

  /**
   * 获取所有注册的页面
   * @returns {Array}
   */
  getRegisteredPages() {
    return Array.from(this.pages.values()).map(pageInfo => ({
      name: pageInfo.PageClass.name,
      pattern: pageInfo.pattern
    }));
  }
}

// 创建全局页面管理器实例
window.PluginPageManager = window.PluginPageManager || new PageManager();

export default window.PluginPageManager;
