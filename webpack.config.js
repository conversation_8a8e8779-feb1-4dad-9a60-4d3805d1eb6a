const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const CssMinimizerPlugin = require("css-minimizer-webpack-plugin");
const { DefinePlugin } = require('webpack');
const { VueLoaderPlugin } = require('vue-loader')
const CopyPlugin = require('copy-webpack-plugin');

module.exports = {
    mode: process.env.NODE_ENV == 'production' ? 'production' : 'development',
    entry: {
        content: './src/content/index.js',
        background: './src/background/index.js',
        fileInterceptor: './src/content/fileInterceptor.js'
    },
    output: {
        path: path.resolve(__dirname, 'dist'),
        filename: 'js/[name].js',
        chunkFilename: 'js/[name].chunk.js',
        assetModuleFilename: 'asset/[hash:8][ext][query]',
        clean: true
    },
    module: {
        rules: [
            {
                test: /\.jsx?$/,
                exclude: /(node_modules|public)/,
                use: {
                    loader: 'babel-loader',
                    options: {
                        presets: [['@babel/preset-env', { useBuiltIns: 'usage', corejs: 3 }]],
                        cacheDirectory: true,
                        cacheCompression: false,
                        plugins: ['@babel/plugin-transform-runtime']
                    }
                }
            },
            {
                test: /\.tsx?$/,
                exclude: /node_modules/,
                use: [{
                    loader: 'babel-loader',
                    options: {
                        presets: [['@babel/preset-env', { useBuiltIns: 'usage', corejs: 3 }]],
                        cacheDirectory: true,
                        cacheCompression: false,
                        plugins: ['@babel/plugin-transform-runtime']
                    }
                }, {
                    loader: "ts-loader",
                    options: {
                        configFile: path.resolve(process.cwd(), 'tsconfig.json'),
                        appendTsSuffixTo: [/\.vue$/]
                    },
                }]
            },
            {
                test: /\.css$/,
                use: [
                    process.env.NODE_ENV == 'production' ? MiniCssExtractPlugin.loader : 'vue-style-loader',
                    "css-loader",
                    {
                        loader: 'postcss-loader',
                        options: {
                            postcssOptions: {
                                plugins: [
                                    [
                                        'postcss-preset-env',
                                    ],
                                ],
                            }
                        }
                    }
                ],
            },
            {
                test: /\.s[ac]ss$/,
                use: [
                    process.env.NODE_ENV == 'production' ? MiniCssExtractPlugin.loader : 'vue-style-loader',
                    "css-loader",
                    {
                        loader: 'postcss-loader',
                        options: {
                            postcssOptions: {
                                plugins: [
                                    [
                                        'postcss-preset-env',
                                    ],
                                ],
                            }
                        }
                    },
                    'sass-loader',
                ],
            },
            {
                test: /\.vue$/,
                loader: 'vue-loader',
                options: {
                    cacheDirectory: path.resolve(__dirname, "node_modules/.cache/vue-loader")
                }
            },
            {
                test: /\.(png|jpe?g|gif|webp|svg)$/,
                type: "asset",
                parser: {
                    dataUrlCondition: {
                        maxSize: 10 * 1024
                    }
                },
                generator: {
                    filename: 'img/[hash:8][ext][query]'
                }
            },
            {
                test: /\.(ttf|woff2?|mp3|mp4|avi)$/,
                type: 'asset/resource',
            }
        ]
    },
    plugins: [
        new MiniCssExtractPlugin({
            filename: 'css/[name].css',
            chunkFilename: 'css/[name].chunk.css'
        }),
        process.env.NODE_ENV == 'production' && new CssMinimizerPlugin(),//css压缩
        new CopyPlugin({
            patterns: [
                {
                    from: path.resolve(__dirname, "public"),
                    toType: "dir",
                    globOptions: {
                        ignore: ["**/index.html"]
                    },
                    info: {
                        minimized: true
                    }
                },
                {
                    from: path.resolve(__dirname, "src/_locales"),
                    to: "_locales",
                    toType: "dir",
                },
                {
                    from: path.resolve(__dirname, "src/manifest.json"),
                    to: "manifest.json",
                    transform(content, path) {
                        const manifest = JSON.parse(content.toString());
                        const package = require('./package.json');
                        manifest.version = package.version;
                        return JSON.stringify(manifest, null, 2);
                    }

                },
                {
                    from: path.resolve(__dirname, "src/content"),
                    to: "js/content",
                    toType: "dir",
                    globOptions: {
                        ignore: ["**/index.js"] // index.js已经通过entry处理
                    }
                },

            ],
        }),
        new VueLoaderPlugin(),
        new DefinePlugin({
            __VUE_OPTIONS_API__: 'true',
            __VUE_PROD_DEVTOOLS__: "false"
        })
    ].filter(Boolean),
    optimization: {
        splitChunks: {
            chunks: 'all'
        },
    },
    resolve: {
        extensions: ['.vue', '.js', '.ts', '.json'],
        alias: {
            '@': path.resolve(__dirname, 'src'),
        },
    },
    devtool: process.env.NODE_ENV == 'production' ? 'hidden-source-map' : 'inline-cheap-source-map',
}
