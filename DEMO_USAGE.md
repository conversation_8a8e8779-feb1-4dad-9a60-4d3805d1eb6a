# 新页面功能添加Demo使用说明

## 📋 概述

本文档演示如何在重构后的架构中添加新的页面自动化功能。以Amazon库存健康页面为例，展示完整的开发流程。

## 🎯 Demo功能

**目标页面**: `/inventoryplanning/manageinventoryhealth?ref_=xx_invplan_favb_xx`
**监听接口**: `/revenuecalculator/getbatchfeesamount`
**功能**: 拦截接口响应，提取并显示`feesAmountRequestList`数据

## 🚀 快速开始

### 1. 测试现有功能

1. 构建项目：`npm run build`
2. 在Chrome中加载`dist`文件夹作为扩展
3. 访问Amazon产品政策页面测试原有功能

### 2. 测试新Demo功能

1. 访问Amazon库存健康页面：
   ```
   https://sellercentral.amazon.com/inventoryplanning/manageinventoryhealth?ref_=xx_invplan_favb_xx
   ```

2. 插件会自动检测页面并显示监控窗口

3. 触发费用计算接口调用（通过页面操作）

4. 观察监控窗口中的数据显示

## 📁 新增文件结构

```
src/content/pages/amazon/
└── InventoryHealthPage.js    # 库存健康页面处理器
```

## 🔧 添加新页面功能的步骤

### 步骤1: 创建页面处理器

创建新的页面处理器类，继承`PageBase`：

```javascript
// src/content/pages/amazon/YourNewPage.js
import PageBase from '../../core/PageBase.js';
import AmazonInterceptor from '../../interceptors/AmazonInterceptor.js';

class YourNewPage extends PageBase {
  shouldActivate() {
    // 定义页面匹配逻辑
    return window.location.href.includes('/your-target-path');
  }
  
  async setupInterceptors() {
    // 设置API拦截器
  }
  
  async setupProcessors() {
    // 设置数据处理逻辑
  }
  
  async setupUI() {
    // 设置UI组件
  }
}

export default YourNewPage;
```

### 步骤2: 注册页面处理器

在`src/content/index.js`中注册新页面：

```javascript
// 1. 导入新页面处理器
const YourNewPage = await loadModule('pages/amazon/YourNewPage.js');

// 2. 注册页面匹配规则
pageManager.registerPage({
  domain: 'amazon',
  path: '/your-target-path',
  params: {
    'param1': 'value1'
  }
}, YourNewPage);
```

### 步骤3: 构建和测试

```bash
npm run build
```

## 📊 Demo代码解析

### 页面匹配逻辑

```javascript
shouldActivate() {
  const currentUrl = window.location.href;
  
  // 检查URL路径
  if (!currentUrl.includes('/inventoryplanning/manageinventoryhealth')) {
    return false;
  }
  
  // 检查参数
  const urlObj = new URL(currentUrl);
  const refParam = urlObj.searchParams.get('ref_');
  if (refParam !== 'xx_invplan_favb_xx') {
    return false;
  }
  
  return true;
}
```

### API拦截设置

```javascript
async setupInterceptors() {
  this.interceptor = new AmazonInterceptor({
    endpoints: ['/revenuecalculator/getbatchfeesamount']
  });
  
  this.interceptor.activate();
}
```

### 数据处理逻辑

```javascript
processFeeData(responseData) {
  const feesAmountRequestList = responseData?.feesAmountRequestList || [];
  
  feesAmountRequestList.forEach((item, index) => {
    const { asin, mSku, isFBA, clientId } = item;
    console.log(`${index + 1}. ASIN: ${asin}, SKU: ${mSku}, FBA: ${isFBA}`);
  });
}
```

## 🎨 UI组件使用

### 状态窗口

```javascript
this.statusWindow = new StatusWindow({
  title: '📦 你的功能标题',
  position: { top: '20px', left: '20px' },
  width: '400px'
});

this.statusWindow.create();
this.statusWindow.update('log', '日志信息');
this.statusWindow.update('action', '当前操作');
```

## 🔄 事件系统

### 监听事件

```javascript
// 监听特定事件
this.eventBus.on('amazon:revenue-fees', this.handleData.bind(this));

// 监听通用事件
this.eventBus.on('amazon:generic', this.handleGenericData.bind(this));
```

### 发送事件

```javascript
this.eventBus.emit('custom:event', {
  data: 'your data'
});
```

## 🛠️ 扩展拦截器

### 添加新的接口监听

在`AmazonInterceptor.js`中添加新的处理方法：

```javascript
// 1. 在handleResponse中添加条件
else if (url.includes('/your-new-endpoint')) {
  this.handleYourNewEndpoint(url, jsonData);
}

// 2. 实现处理方法
handleYourNewEndpoint(url, data) {
  console.log('处理新接口:', url);
  
  this.emitInterceptedData('your-endpoint', url, data);
  
  this.eventBus.emit('amazon:your-endpoint', {
    url,
    data
  });
}
```

## 📝 最佳实践

1. **命名规范**: 使用描述性的类名和方法名
2. **错误处理**: 在关键方法中添加try-catch
3. **日志记录**: 使用`this.log()`记录重要操作
4. **资源清理**: 在`cleanup()`方法中清理资源
5. **事件解绑**: 页面切换时自动清理事件监听

## 🔍 调试技巧

1. **控制台日志**: 查看插件初始化和页面匹配日志
2. **网络面板**: 确认API请求被正确拦截
3. **状态窗口**: 实时查看处理状态和数据
4. **事件总线**: 使用`window.PluginEventBus`调试事件

## 🚨 常见问题

1. **模块加载失败**: 确保文件路径正确且已构建
2. **页面不匹配**: 检查URL匹配逻辑和参数
3. **接口未拦截**: 确认接口路径和拦截器配置
4. **事件未触发**: 检查事件名称和监听器绑定

## 📈 性能优化

1. **按需加载**: 只在匹配页面时加载相关模块
2. **事件解绑**: 页面切换时清理事件监听器
3. **内存管理**: 及时清理大型数据对象
4. **异步处理**: 使用异步方法处理耗时操作
