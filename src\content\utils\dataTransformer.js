/**
 * 数据转换工具函数
 */

/**
 * MD5加密函数（简化版）
 * @param {string} text 要加密的文本
 * @returns {string} 哈希值
 */
export function md5Encrypt(text) {
  // 简单的哈希函数，实际项目中应该使用crypto-js等库
  let hash = 0;
  if (text.length === 0) return hash.toString();
  for (let i = 0; i < text.length; i++) {
    const char = text.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString();
}

/**
 * UTC时间转换为CST时间
 * @param {string} utcDateStr UTC时间字符串
 * @returns {string} CST日期字符串
 */
export function convertUtcToCst(utcDateStr) {
  try {
    const date = new Date(utcDateStr);
    const cstDate = new Date(date.getTime() + 8 * 60 * 60 * 1000);
    return cstDate.toISOString().split('T')[0];
  } catch (e) {
    console.error('时间转换错误:', e);
    return utcDateStr;
  }
}

/**
 * 转换中文日期为时间戳
 * @param {string} dateStr 中文日期字符串
 * @returns {number} 时间戳
 */
export function convertChineseDateToTimestamp(dateStr) {
  if (!dateStr) return 0;

  try {
    if (dateStr.includes("年") && dateStr.includes("月") && dateStr.includes("日")) {
      const year = parseInt(dateStr.split("年")[0]);
      const month = parseInt(dateStr.split("年")[1].split("月")[0]);
      const day = parseInt(dateStr.split("月")[1].split("日")[0]);
      const date = new Date(year, month - 1, day);
      return Math.floor(date.getTime() / 1000);
    } else {
      const date = new Date(dateStr);
      return Math.floor(date.getTime() / 1000);
    }
  } catch (e) {
    console.error('时间格式化出错:', e);
    return dateStr;
  }
}

/**
 * 获取SKU和ASIN数量
 * @param {Array} issueList 问题列表
 * @returns {Object} SKU和ASIN数量
 */
export function getSkuAsinCount(issueList) {
  const skus = new Set();
  const asins = new Set();

  issueList.forEach(issue => {
    const sku = issue.target?.artifactId;
    const asin = issue.parameters?.asin;
    if (sku) skus.add(sku);
    if (asin) asins.add(asin);
  });

  return { skus: skus.size, asins: asins.size };
}

/**
 * 提取缺陷项目的关键信息
 * @param {Object} item 缺陷项目
 * @returns {Object} 提取的信息
 */
export function extractDefectInfo(item) {
  const issue = item.issueList?.[0] || {};
  
  return {
    msgType: issue.parameters?.message_id || "",
    policyEntityId: issue.parameters?.policy_id || "",
    sku: issue.target?.artifactId || "",
    asin: issue.parameters?.asin || "",
    brand: issue.parameters?.brand || "",
    policyName: issue.parameters?.policy_name || "",
    impactDate: issue.parameters?.impactDate || "",
    dueDate: issue.parameters?.due_date || "",
    status: item.status || ""
  };
}

/**
 * 判断风险等级
 * @param {string} policyName 政策名称
 * @returns {number} 风险等级 (0: 未知, 1: 中低风险, 2: 高风险)
 */
export function determineRiskLevel(policyName) {
  if (policyName === "GPSR_Reactive_HighRisk_PSI_DE") {
    return 2; // 高风险
  } else if (policyName === "GPSR_Reactive_MedLow-Risk_PSI_DE") {
    return 1; // 中低风险
  }
  return 0; // 未知
}

/**
 * 生成唯一ID
 * @param {Object} data 数据对象
 * @returns {string} 唯一ID
 */
export function generateUniqueId(data) {
  const {
    storeName = '',
    storeSite = '',
    msg = '',
    asin = '',
    sku = '',
    status = '',
    startTime = '',
    dueTime = ''
  } = data;
  
  const combinedString = storeName + storeSite + msg + asin + sku + status + startTime + dueTime;
  return md5Encrypt(combinedString);
}

/**
 * 构建数据库插入数据
 * @param {Object} defectInfo 缺陷信息
 * @param {Object} additionalData 附加数据
 * @returns {Object} 数据库插入数据
 */
export function buildInsertData(defectInfo, additionalData = {}) {
  const {
    storeName = "default_store",
    storeSite = "default_site",
    msg = "",
    uniqueId = "",
    currentTimestamp = Math.floor(Date.now() / 1000),
    skuAsinCount = { skus: 0, asins: 0 },
    hasProductSafetyCert = 0,
    startTime = 0,
    dueTime = 0
  } = additionalData;

  return {
    unique_id: uniqueId,
    platform_account: storeName,
    platform_site: storeSite,
    type: msg,
    asin: defectInfo.asin,
    sku: defectInfo.sku,
    status: defectInfo.status,
    platform_time: startTime,
    platform_end_time: dueTime,
    create_time: currentTimestamp,
    brand: defectInfo.brand,
    asin_num: skuAsinCount.asins,
    sku_num: skuAsinCount.skus,
    has_product_safety_cert: hasProductSafetyCert,
    is_click: 0, // 默认值，后续会根据处理结果更新
    data_status: 0 // 默认值，后续会根据处理结果更新
  };
}

/**
 * 解析政策详情中的resolution paths
 * @param {Object} policyDetail 政策详情
 * @returns {string} resolution paths ID
 */
export function extractResolutionPaths(policyDetail) {
  try {
    const resolutionConfig = policyDetail.data?.policies?.[0]?.policyAttributeNameToTypeValue?.resolutionConfig?.attributeValue;
    if (resolutionConfig) {
      const parsed = JSON.parse(resolutionConfig);
      return parsed.ResolutionPaths?.[2]?.id || "";
    }
  } catch (e) {
    console.error('❌ 获取resolutionConfig出错:', e);
  }
  return "";
}

/**
 * 检查是否为政策详情数据
 * @param {string} msgType 消息类型
 * @returns {boolean}
 */
export function isPolicyDetailData(msgType) {
  return msgType === "sp_health_detail_table_reason_psi_details";
}

/**
 * 格式化时间戳为可读字符串
 * @param {number} timestamp 时间戳
 * @returns {string} 格式化的时间字符串
 */
export function formatTimestamp(timestamp) {
  if (!timestamp) return '';
  const date = new Date(timestamp * 1000);
  return date.toLocaleString('zh-CN');
}

/**
 * 验证必需字段
 * @param {Object} data 要验证的数据
 * @param {Array<string>} requiredFields 必需字段列表
 * @returns {Object} 验证结果
 */
export function validateRequiredFields(data, requiredFields) {
  const missing = [];
  const invalid = [];

  requiredFields.forEach(field => {
    if (!(field in data)) {
      missing.push(field);
    } else if (!data[field] && data[field] !== 0) {
      invalid.push(field);
    }
  });

  return {
    isValid: missing.length === 0 && invalid.length === 0,
    missing,
    invalid
  };
}

/**
 * 深度克隆对象
 * @param {*} obj 要克隆的对象
 * @returns {*} 克隆后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item));
  }

  if (typeof obj === 'object') {
    const cloned = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }

  return obj;
}

/**
 * 安全的JSON解析
 * @param {string} jsonString JSON字符串
 * @param {*} defaultValue 默认值
 * @returns {*} 解析结果或默认值
 */
export function safeJsonParse(jsonString, defaultValue = null) {
  try {
    return JSON.parse(jsonString);
  } catch (e) {
    console.warn('JSON解析失败:', e);
    return defaultValue;
  }
}

/**
 * 延迟函数
 * @param {number} ms 延迟毫秒数
 * @returns {Promise}
 */
export function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 随机延迟
 * @param {number} min 最小延迟毫秒数
 * @param {number} max 最大延迟毫秒数
 * @returns {Promise}
 */
export function randomDelay(min, max) {
  const ms = Math.random() * (max - min) + min;
  return delay(ms);
}
