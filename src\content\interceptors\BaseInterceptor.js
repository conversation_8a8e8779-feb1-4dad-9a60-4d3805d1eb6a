import EventBus from '../core/EventBus.js';

/**
 * 基础拦截器类
 * 所有拦截器都应该继承此类
 */
class BaseInterceptor {
  constructor(config = {}) {
    this.config = config;
    this.isActive = false;
    this.eventBus = EventBus;
    this.originalFetch = null;
    this.interceptedUrls = new Set();
    
    // 绑定方法到实例
    this.interceptFetch = this.interceptFetch.bind(this);
  }

  /**
   * 激活拦截器
   */
  activate() {
    if (this.isActive) {
      console.warn(`⚠️ ${this.constructor.name} 已经激活`);
      return;
    }

    console.log(`🔧 激活拦截器: ${this.constructor.name}`);
    
    // 保存原始fetch
    this.originalFetch = window.fetch;
    
    // 替换fetch
    window.fetch = this.interceptFetch;
    
    this.isActive = true;
    
    // 触发激活事件
    this.eventBus.emit('interceptor:activated', {
      interceptor: this.constructor.name,
      config: this.config
    });
  }

  /**
   * 停用拦截器
   */
  deactivate() {
    if (!this.isActive) {
      console.warn(`⚠️ ${this.constructor.name} 未激活`);
      return;
    }

    console.log(`🔧 停用拦截器: ${this.constructor.name}`);
    
    // 恢复原始fetch
    if (this.originalFetch) {
      window.fetch = this.originalFetch;
      this.originalFetch = null;
    }
    
    this.isActive = false;
    
    // 触发停用事件
    this.eventBus.emit('interceptor:deactivated', {
      interceptor: this.constructor.name
    });
  }

  /**
   * 拦截fetch请求
   * @param {...any} args fetch参数
   * @returns {Promise<Response>}
   */
  async interceptFetch(...args) {
    const [resource] = args;
    const url = typeof resource === "string" ? resource : resource.url;
    
    // 调用原始fetch
    const response = await this.originalFetch(...args);
    
    // 检查是否需要拦截此URL
    if (this.shouldIntercept(url)) {
      console.log(`📡 拦截请求: ${url}`);
      this.interceptedUrls.add(url);
      
      // 克隆响应以避免消费
      const clone = response.clone();
      
      try {
        // 处理响应
        await this.handleResponse(url, clone, args);
      } catch (error) {
        console.error(`❌ 处理响应失败 (${url}):`, error);
        this.eventBus.emit('interceptor:error', {
          interceptor: this.constructor.name,
          url,
          error: error.message
        });
      }
    }
    
    return response;
  }

  /**
   * 检查是否应该拦截此URL
   * 子类应该重写此方法
   * @param {string} url 请求URL
   * @returns {boolean}
   */
  shouldIntercept(url) {
    // 默认不拦截任何请求
    return false;
  }

  /**
   * 处理拦截到的响应
   * 子类应该重写此方法
   * @param {string} url 请求URL
   * @param {Response} response 响应对象
   * @param {Array} requestArgs 原始请求参数
   */
  async handleResponse(url, response, requestArgs) {
    // 默认实现为空，子类应该重写
  }

  /**
   * 解析JSON响应
   * @param {Response} response 响应对象
   * @returns {Promise<Object|null>}
   */
  async parseJsonResponse(response) {
    try {
      const text = await response.text();
      return JSON.parse(text);
    } catch (error) {
      console.warn(`⚠️ JSON解析失败:`, error);
      return null;
    }
  }

  /**
   * 发送拦截到的数据
   * @param {string} type 数据类型
   * @param {string} url 请求URL
   * @param {*} data 响应数据
   */
  emitInterceptedData(type, url, data) {
    const eventData = {
      source: 'interceptor',
      interceptor: this.constructor.name,
      type,
      url,
      data,
      timestamp: Date.now()
    };

    // 发送到事件总线
    this.eventBus.emit('interceptor:data', eventData);
    
    // 发送到window消息（兼容现有代码）
    window.postMessage({
      source: 'amazon-api-interceptor',
      type,
      url,
      data
    }, '*');
    
    console.log(`📤 发送拦截数据: ${type}`, { url, dataSize: JSON.stringify(data).length });
  }

  /**
   * 获取拦截统计信息
   * @returns {Object}
   */
  getStats() {
    return {
      interceptor: this.constructor.name,
      isActive: this.isActive,
      interceptedUrls: Array.from(this.interceptedUrls),
      interceptedCount: this.interceptedUrls.size
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.interceptedUrls.clear();
    console.log(`📊 ${this.constructor.name} 统计信息已重置`);
  }
}

export default BaseInterceptor;
