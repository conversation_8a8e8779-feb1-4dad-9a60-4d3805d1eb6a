/**
 * URL匹配工具函数
 */

/**
 * 检查URL是否匹配Amazon域名
 * @param {string} url 要检查的URL
 * @returns {boolean}
 */
export function isAmazonDomain(url) {
  return url.includes('amazon.') || url.includes('sellercentral.');
}

/**
 * 检查URL路径是否匹配
 * @param {string} url 当前URL
 * @param {string} expectedPath 期望的路径
 * @returns {boolean}
 */
export function matchPath(url, expectedPath) {
  const urlObj = new URL(url);
  return urlObj.pathname.includes(expectedPath);
}

/**
 * 检查URL参数是否匹配
 * @param {string} url 当前URL
 * @param {Object} expectedParams 期望的参数对象
 * @returns {boolean}
 */
export function matchParams(url, expectedParams) {
  const urlObj = new URL(url);
  const urlParams = new URLSearchParams(urlObj.search);
  
  for (const [key, value] of Object.entries(expectedParams)) {
    if (urlParams.get(key) !== value) {
      return false;
    }
  }
  
  return true;
}

/**
 * 增强的URL检查函数
 * @param {string} url 要检查的URL
 * @param {Object} config 检查配置
 * @returns {Object} 检查结果
 */
export function validatePageUrl(url, config = {}) {
  const result = {
    isValid: false,
    reason: '',
    details: {}
  };

  const urlObj = new URL(url);
  
  console.log('🔍 URL验证详情:', {
    url,
    hostname: urlObj.hostname,
    pathname: urlObj.pathname,
    search: urlObj.search
  });

  // 检查域名
  if (config.domain && !isAmazonDomain(url)) {
    result.reason = '不是Amazon域名';
    result.details.expectedDomain = config.domain;
    result.details.actualDomain = urlObj.hostname;
    return result;
  }

  // 检查路径
  if (config.path && !matchPath(url, config.path)) {
    result.reason = '路径不匹配';
    result.details.expectedPath = config.path;
    result.details.actualPath = urlObj.pathname;
    return result;
  }

  // 检查必需参数
  if (config.requiredParams && !matchParams(url, config.requiredParams)) {
    result.reason = '必需参数不匹配';
    result.details.expectedParams = config.requiredParams;
    result.details.actualParams = Object.fromEntries(new URLSearchParams(urlObj.search));
    return result;
  }

  // 检查可选参数
  if (config.optionalParams) {
    const urlParams = new URLSearchParams(urlObj.search);
    const missingOptional = [];
    
    for (const [key, value] of Object.entries(config.optionalParams)) {
      if (urlParams.get(key) !== value) {
        missingOptional.push({ key, expected: value, actual: urlParams.get(key) });
      }
    }
    
    if (missingOptional.length > 0) {
      result.reason = '可选参数不匹配';
      result.details.missingOptional = missingOptional;
      result.details.shouldShowButton = true; // 可以显示按钮
      return result;
    }
  }

  result.isValid = true;
  result.reason = '完全匹配';
  return result;
}

/**
 * Amazon产品政策页面URL验证
 * @param {string} url 当前URL
 * @returns {Object} 验证结果
 */
export function validateAmazonPolicyUrl(url) {
  return validatePageUrl(url, {
    domain: 'amazon',
    path: '/performance/account/health/product-policies',
    requiredParams: {
      't': 'regulatory-compliance'
    },
    optionalParams: {
      'script': '1'
    }
  });
}

/**
 * 生成带参数的URL
 * @param {string} baseUrl 基础URL
 * @param {Object} params 要添加的参数
 * @returns {string} 新的URL
 */
export function addUrlParams(baseUrl, params) {
  const url = new URL(baseUrl);
  
  for (const [key, value] of Object.entries(params)) {
    url.searchParams.set(key, value);
  }
  
  return url.toString();
}

/**
 * 移除URL参数
 * @param {string} url 原始URL
 * @param {Array<string>} paramNames 要移除的参数名
 * @returns {string} 新的URL
 */
export function removeUrlParams(url, paramNames) {
  const urlObj = new URL(url);
  
  paramNames.forEach(paramName => {
    urlObj.searchParams.delete(paramName);
  });
  
  return urlObj.toString();
}

/**
 * 获取URL参数值
 * @param {string} url URL字符串
 * @param {string} paramName 参数名
 * @returns {string|null} 参数值
 */
export function getUrlParam(url, paramName) {
  const urlObj = new URL(url);
  return urlObj.searchParams.get(paramName);
}

/**
 * 检查URL是否包含特定参数
 * @param {string} url URL字符串
 * @param {string} paramName 参数名
 * @returns {boolean}
 */
export function hasUrlParam(url, paramName) {
  const urlObj = new URL(url);
  return urlObj.searchParams.has(paramName);
}

/**
 * URL模式匹配器
 */
export class UrlMatcher {
  constructor(patterns = []) {
    this.patterns = patterns;
  }

  /**
   * 添加匹配模式
   * @param {Object} pattern 匹配模式
   */
  addPattern(pattern) {
    this.patterns.push(pattern);
  }

  /**
   * 查找匹配的模式
   * @param {string} url 要匹配的URL
   * @returns {Object|null} 匹配的模式
   */
  findMatch(url) {
    for (const pattern of this.patterns) {
      if (this.matchPattern(url, pattern)) {
        return pattern;
      }
    }
    return null;
  }

  /**
   * 检查URL是否匹配模式
   * @param {string} url URL字符串
   * @param {Object} pattern 匹配模式
   * @returns {boolean}
   */
  matchPattern(url, pattern) {
    const urlObj = new URL(url);

    // 检查域名
    if (pattern.domain && !urlObj.hostname.includes(pattern.domain)) {
      return false;
    }

    // 检查路径
    if (pattern.path && !urlObj.pathname.includes(pattern.path)) {
      return false;
    }

    // 检查参数
    if (pattern.params) {
      const urlParams = new URLSearchParams(urlObj.search);
      for (const [key, value] of Object.entries(pattern.params)) {
        if (urlParams.get(key) !== value) {
          return false;
        }
      }
    }

    // 检查自定义匹配函数
    if (pattern.customMatcher && typeof pattern.customMatcher === 'function') {
      return pattern.customMatcher(url, urlObj);
    }

    return true;
  }

  /**
   * 获取所有模式
   * @returns {Array} 模式数组
   */
  getPatterns() {
    return [...this.patterns];
  }

  /**
   * 清空所有模式
   */
  clearPatterns() {
    this.patterns = [];
  }
}
