/**
 * Content Script 主入口文件
 * 负责页面检测和分发到对应的页面处理器
 */

// 动态导入模块
async function loadModule(path) {
  try {
    const module = await import(chrome.runtime.getURL(`js/content/${path}`));
    return module.default;
  } catch (error) {
    console.error(`❌ 加载模块失败: ${path}`, error);
    return null;
  }
}

// 主初始化函数
async function initializePlugin() {
  console.log("📦 Content script loaded");
  console.log('🚀 插件开始初始化...');

  try {
    // 加载核心模块
    const PageManager = await loadModule('core/PageManager.js');
    const ProductPolicyPage = await loadModule('pages/amazon/ProductPolicyPage.js');
    const InventoryHealthPage = await loadModule('pages/amazon/InventoryHealthPage.js');

    if (!PageManager || !ProductPolicyPage || !InventoryHealthPage) {
      console.error('❌ 核心模块加载失败');
      return;
    }

    // 创建页面管理器实例
    const pageManager = new PageManager();

    // 注册Amazon产品政策页面处理器
    pageManager.registerPage({
      domain: 'amazon',
      path: '/performance/account/health/product-policies',
      params: {
        't': 'regulatory-compliance'
      },
      customMatcher: (url) => {
        // 自定义匹配逻辑：检查是否是Amazon域名
        return url.includes('amazon.') || url.includes('sellercentral.');
      }
    }, ProductPolicyPage);

    // 注册Amazon库存健康页面处理器 (Demo)
    pageManager.registerPage({
      domain: 'amazon',
      path: '/inventoryplanning/manageinventoryhealth',
      params: {
        'ref_': 'xx_invplan_favb_xx'
      },
      customMatcher: (url) => {
        // 自定义匹配逻辑：检查是否是Amazon域名
        return url.includes('amazon.') || url.includes('sellercentral.');
      }
    }, InventoryHealthPage);

    // 初始化页面管理器
    await pageManager.initialize();

    console.log('✅ 插件初始化完成');

  } catch (error) {
    console.error('❌ 插件初始化失败:', error);
  }
}

// 注入拦截器脚本（保持兼容性）
function injectInterceptorScript() {
  const script = document.createElement('script');
  script.src = chrome.runtime.getURL('js/fileInterceptor.js');

  // 安全地添加到页面
  if (document.head) {
    document.head.appendChild(script);
  } else if (document.documentElement) {
    document.documentElement.appendChild(script);
  } else {
    console.error('❌ Cannot find head or documentElement to inject script');
  }
}

// 启动插件
(async function() {
  // 注入拦截器脚本
  injectInterceptorScript();

  // 初始化插件
  await initializePlugin();
})();