import PageBase from '../../core/PageBase.js';
import AmazonInterceptor from '../../interceptors/AmazonInterceptor.js';
import StatusWindow from '../../ui/StatusWindow.js';
import { validateAmazonPolicyUrl, addUrlParams } from '../../utils/urlMatcher.js';
import { 
  extractDefectInfo, 
  determineRiskLevel, 
  generateUniqueId, 
  buildInsertData,
  extractResolutionPaths,
  isPolicyDetailData,
  getSkuAsinCount,
  convertChineseDateToTimestamp,
  convertUtcToCst,
  randomDelay
} from '../../utils/dataTransformer.js';

/**
 * Amazon产品政策页面处理器
 * 处理Amazon产品政策合规性相关功能
 */
class ProductPolicyPage extends PageBase {
  constructor(config = {}) {
    super(config);
    
    // 页面特定配置
    this.pageConfig = {
      storeName: "default_store",
      storeSite: "default_site",
      isTestMode: false,
      maxConsecutiveFailedPages: 3,
      ...config
    };
    
    // 状态数据
    this.state = {
      totalNum: 0,
      normalDataMap: {},
      clickedSkuList: [],
      currentAsins: [],
      totalProgress: 0,
      consecutiveFailedPages: 0,
      sellerId: "",
      domain: "",
      antiCsrfToken: "",
      submittedCount: 0,
      skippedCount: 0,
      nonWarningSkippedCount: 0,
      highRiskSkippedList: []
    };
    
    // 组件实例
    this.interceptor = null;
    this.statusWindow = null;
  }

  /**
   * 检查当前页面是否应该激活此处理器
   * @returns {boolean}
   */
  shouldActivate() {
    const currentUrl = window.location.href;
    const validation = validateAmazonPolicyUrl(currentUrl);
    
    this.log('info', 'URL验证结果', validation);
    
    if (!validation.isValid) {
      // 如果是可选参数不匹配，显示按钮
      if (validation.details.shouldShowButton) {
        this.createAutomationButton();
      }
      return false;
    }
    
    return true;
  }

  /**
   * 设置拦截器
   */
  async setupInterceptors() {
    this.interceptor = new AmazonInterceptor({
      endpoints: [
        '/performance/api/product/policy/defects/pagination',
        '/performance/api/summary',
        'zh-CN.json'
      ]
    });
    
    this.interceptors.push(this.interceptor);
    this.interceptor.activate();
    
    this.log('info', 'Amazon拦截器已激活');
  }

  /**
   * 设置数据处理器
   */
  async setupProcessors() {
    // 监听拦截器事件
    this.eventBus.on('amazon:summary', this.handleSummaryData.bind(this));
    this.eventBus.on('amazon:localization', this.handleLocalizationData.bind(this));
    this.eventBus.on('amazon:defects-pagination', this.handleDefectsPagination.bind(this));
    
    this.log('info', '数据处理器已设置');
  }

  /**
   * 设置UI组件
   */
  async setupUI() {
    // 状态窗口会在收到summary数据后创建
    this.log('info', 'UI组件已设置');
  }

  /**
   * 创建自动化按钮
   */
  createAutomationButton() {
    // 等待页面加载完成
    if (document.readyState !== 'loading') {
      this.addAutomationButton();
    } else {
      document.addEventListener('DOMContentLoaded', () => this.addAutomationButton());
    }
  }

  /**
   * 添加自动化按钮到页面
   */
  addAutomationButton() {
    const interval = setInterval(() => {
      const targetButton = document.querySelector('[data-testid="ahd-pp-filter-button"]');
      if (targetButton) {
        clearInterval(interval);
        
        // 创建按钮
        const button = document.createElement('button');
        button.id = 'start-automation-button';
        button.textContent = '🚀 启动政策合规性自动化';
        button.style.cssText = `
          background: linear-gradient(135deg, #007cba, #005a87);
          color: white;
          border: none;
          border-radius: 6px;
          padding: 8px 16px;
          font-size: 14px;
          font-weight: bold;
          cursor: pointer;
          box-shadow: 0 2px 6px rgba(0,0,0,0.2);
          transition: all 0.3s ease;
          margin-left: 10px;
          vertical-align: middle;
          animation: pulse 2s infinite;
        `;

        // 添加脉冲动画样式
        const style = document.createElement('style');
        style.textContent = `
          @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.03); }
            100% { transform: scale(1); }
          }
          
          #start-automation-button:hover {
            background: linear-gradient(135deg, #005a87, #003d66);
            transform: scale(1.05);
            box-shadow: 0 4px 10px rgba(0,0,0,0.3);
          }
        `;
        document.head.appendChild(style);

        // 点击事件 - 跳转到带script=1参数的URL
        button.addEventListener('click', () => {
          const currentUrl = addUrlParams(window.location.href, { script: '1' });
          window.location.href = currentUrl;
        });

        // 将按钮插入到目标按钮后面
        targetButton.parentNode.insertBefore(button, targetButton.nextSibling);

        this.log('info', '自动化按钮已添加到页面');
      }
    }, 500);

    // 设置超时时间，避免无限等待
    setTimeout(() => {
      clearInterval(interval);
    }, 10000);
  }

  /**
   * 处理摘要数据
   * @param {Object} eventData 事件数据
   */
  handleSummaryData(eventData) {
    this.log('info', '处理摘要数据', eventData);
    
    this.state.totalNum = eventData.totalCount || 0;
    this.state.domain = window.location.hostname;
    
    // 创建状态窗口
    if (!this.statusWindow) {
      this.statusWindow = new StatusWindow({
        isTestMode: this.pageConfig.isTestMode
      });
      this.statusWindow.create();
      this.uiComponents.push(this.statusWindow);
    }
    
    // 更新状态窗口
    this.statusWindow.update('total', this.state.totalNum);
    this.statusWindow.update('log', `📊 获取到数据总量: ${this.state.totalNum}`);
    
    if (this.state.totalNum === 0) {
      this.statusWindow.update('action', '没有数据需要处理');
      this.statusWindow.update('log', '📭 没有数据需要处理');
      this.sendMessage({
        type: 'PROCESS_COMPLETE',
        message: '没有数据需要处理'
      });
      return;
    }
  }

  /**
   * 处理本地化数据
   * @param {Object} eventData 事件数据
   */
  handleLocalizationData(eventData) {
    this.log('info', '处理本地化数据');
    this.state.normalDataMap = { ...this.state.normalDataMap, ...eventData.data };
  }

  /**
   * 处理缺陷分页数据
   * @param {Object} eventData 事件数据
   */
  async handleDefectsPagination(eventData) {
    this.log('info', '处理缺陷分页数据', {
      url: eventData.url,
      defectsCount: eventData.defectsCount
    });

    // 获取sellerId
    if (!this.state.sellerId && eventData.sellerId) {
      this.state.sellerId = eventData.sellerId;
      this.statusWindow?.update('log', `🏪 获取到sellerId: ${this.state.sellerId}`);
    }

    if (!this.state.sellerId) {
      this.log('error', '未获取到sellerId');
      this.statusWindow?.update('log', '❌ 未获取到sellerId');
      return;
    }

    // 处理分页数据
    await this.processDefectsPagination(eventData.data);
  }

  /**
   * 处理分页数据
   * @param {Object} responseData 响应数据
   */
  async processDefectsPagination(responseData) {
    const defects = responseData.defects || [];

    // 处理当前页数据
    const { validDataCount } = await this.processDefectsData(defects);

    // 检查是否需要翻页
    if (defects.length === 25) {
      this.log('info', '尝试翻页...');
      this.statusWindow?.update('action', '尝试翻页...');
      this.statusWindow?.update('log', '📄 尝试翻页...');

      const clickResult = await this.clickNextPage();

      if (!clickResult) {
        this.state.consecutiveFailedPages++;
        this.log('warn', `翻页失败，连续失败次数: ${this.state.consecutiveFailedPages}`);
        this.statusWindow?.update('log', `⚠️ 翻页失败，连续失败次数: ${this.state.consecutiveFailedPages}`);

        if (this.state.consecutiveFailedPages >= this.pageConfig.maxConsecutiveFailedPages) {
          this.log('error', `连续${this.pageConfig.maxConsecutiveFailedPages}次翻页失败，停止处理`);
          this.statusWindow?.update('action', '翻页失败，停止处理');
          this.statusWindow?.update('log', `❌ 连续${this.pageConfig.maxConsecutiveFailedPages}次翻页失败，停止处理`);
          return false;
        }
      } else {
        this.state.consecutiveFailedPages = 0;
        this.log('info', '翻页成功，等待页面加载');
        this.statusWindow?.update('log', '✅ 翻页成功，等待页面加载');

        // 验证页面是否正常加载
        const loadResult = await this.waitForPageLoad();
        if (!loadResult) {
          this.state.consecutiveFailedPages++;
          this.log('warn', `页面加载异常，连续失败次数: ${this.state.consecutiveFailedPages}`);
          this.statusWindow?.update('log', `⚠️ 页面加载异常，连续失败次数: ${this.state.consecutiveFailedPages}`);

          if (this.state.consecutiveFailedPages >= this.pageConfig.maxConsecutiveFailedPages) {
            this.log('error', `连续${this.pageConfig.maxConsecutiveFailedPages}次页面加载失败，停止处理`);
            this.statusWindow?.update('action', '页面加载失败，停止处理');
            this.statusWindow?.update('log', `❌ 连续${this.pageConfig.maxConsecutiveFailedPages}次页面加载失败，停止处理`);
            return false;
          }
        } else {
          this.log('info', '页面加载成功，继续处理');
          this.statusWindow?.update('log', '✅ 页面加载成功，继续处理');
        }
      }
    } else {
      this.log('info', '当前页数据量不足25条，已到最后一页');
      this.statusWindow?.update('action', '处理完成');
      this.statusWindow?.update('log', '📄 当前页数据量不足25条，已到最后一页');

      // 显示处理完成统计
      this.showCompletionSummary();
      return false; // 结束处理
    }

    return true; // 继续处理
  }

  /**
   * 处理缺陷数据
   * @param {Array} defects 缺陷数据数组
   * @returns {Object} 处理结果
   */
  async processDefectsData(defects) {
    if (!defects || defects.length === 0) {
      this.log('info', '没有产品政策数据');
      return { validDataCount: 0, skippedCount: 0 };
    }

    this.log('info', `当前数据量: ${defects.length}`);

    let validDataCount = 0;
    let localSkippedCount = 0;

    for (let i = 0; i < defects.length; i++) {
      const item = defects[i];
      const msgType = item.issueList?.[0]?.parameters?.message_id || "";

      if (!isPolicyDetailData(msgType)) {
        localSkippedCount++;
        this.state.nonWarningSkippedCount++;
        this.statusWindow?.update('log', `🚫 跳过非安全警告数据: ${msgType}`);
        continue;
      }

      validDataCount++;
      await this.processDefectItem(item);
    }

    // 更新统计显示
    this.updateStats();

    this.log('info', `当前页面处理了${validDataCount}条有效数据，跳过了${localSkippedCount}条数据`);
    this.statusWindow?.update('log', `📈 当前页面: 处理${validDataCount}条，跳过${localSkippedCount}条`);

    return { validDataCount, skippedCount: localSkippedCount };
  }

  /**
   * 处理单个缺陷项
   * @param {Object} item 缺陷项
   */
  async processDefectItem(item) {
    try {
      const defectInfo = extractDefectInfo(item);
      const msg = this.state.normalDataMap[defectInfo.msgType] || "";

      this.state.totalProgress += 1;

      // 更新进度
      this.updateProgress();

      this.log('info', `处理数据: policyEntityId: ${defectInfo.policyEntityId}, sku: ${defectInfo.sku}`);
      this.statusWindow?.update('action', `处理 SKU: ${defectInfo.sku}`);
      this.statusWindow?.update('log', `📋 开始处理 SKU: ${defectInfo.sku}, ASIN: ${defectInfo.asin}`);

      // 检查是否已处理过
      if (this.state.clickedSkuList.includes(defectInfo.sku)) {
        this.log('info', `已处理过的SKU: ${defectInfo.sku}, 跳过`);
        this.statusWindow?.update('log', `✅ 已处理过的SKU: ${defectInfo.sku}, 跳过`);
        return;
      }

      this.state.currentAsins.push(defectInfo.asin);

      // 时间处理
      const startTime = convertChineseDateToTimestamp(defectInfo.impactDate);
      const dueTime = convertChineseDateToTimestamp(convertUtcToCst(defectInfo.dueDate));

      // 获取SKU和ASIN数量
      const skuAsinCount = getSkuAsinCount(item.issueList);

      // 判断风险等级
      const hasProductSafetyCert = determineRiskLevel(defectInfo.policyName);

      // 生成唯一ID
      const currentTimestamp = Math.floor(Date.now() / 1000);
      const uniqueId = generateUniqueId({
        storeName: this.pageConfig.storeName,
        storeSite: this.pageConfig.storeSite,
        msg,
        asin: defectInfo.asin,
        sku: defectInfo.sku,
        status: defectInfo.status,
        startTime,
        dueTime
      });

      this.log('info', `任务进度: ${this.state.totalProgress}/${this.state.totalNum}`);

      if (!defectInfo.sku || !defectInfo.policyEntityId) {
        this.log('error', 'SKU或policyEntityId为空，跳过');
        return;
      }

      // 构建插入数据
      const insertData = buildInsertData(defectInfo, {
        storeName: this.pageConfig.storeName,
        storeSite: this.pageConfig.storeSite,
        msg,
        uniqueId,
        currentTimestamp,
        skuAsinCount,
        hasProductSafetyCert,
        startTime,
        dueTime
      });

      // 处理业务逻辑
      await this.processBusinessLogic(defectInfo, insertData, hasProductSafetyCert);

      // 等待随机时间
      await randomDelay(1000, 3000);

    } catch (error) {
      this.log('error', '处理数据异常', error);
    }
  }

  /**
   * 处理业务逻辑
   * @param {Object} defectInfo 缺陷信息
   * @param {Object} insertData 插入数据
   * @param {number} hasProductSafetyCert 风险等级
   */
  async processBusinessLogic(defectInfo, insertData, hasProductSafetyCert) {
    // 获取政策详情
    this.log('info', `获取policyEntityId ${defectInfo.policyEntityId} 详情...`);
    this.statusWindow?.update('action', `获取政策详情: ${defectInfo.policyEntityId}`);
    this.statusWindow?.update('log', '🔍 获取政策详情...');

    const detail = await this.getPolicyDetail(defectInfo.policyEntityId);

    if (!detail || !detail.data) {
      this.log('error', '获取政策详情失败');
      this.statusWindow?.update('log', `❌ 获取政策详情失败: ${defectInfo.policyEntityId}`);
      return;
    }

    this.state.antiCsrfToken = detail.antiCsrfToken;
    this.log('info', '获取token成功');
    this.statusWindow?.update('log', '🎫 获取token成功');

    // 获取resolution paths
    const resolutionPaths = extractResolutionPaths(detail);
    this.log('info', `Resolution paths: ${resolutionPaths}`);

    // 等待随机时间
    await randomDelay(1000, 2000);

    // 获取提交批次信息
    this.log('info', '检查提交状态...');
    this.statusWindow?.update('action', `检查提交状态: ${defectInfo.sku}`);
    this.statusWindow?.update('log', '📤 检查提交状态...');

    const nextDetail = await this.getArtifactSubmissionBatch(defectInfo.policyEntityId, resolutionPaths, defectInfo.sku);

    if (!nextDetail) {
      this.log('warn', '无法获取提交信息');
      this.statusWindow?.update('log', `⚠️ 无法获取提交信息: ${defectInfo.sku}`);
      return;
    }

    this.state.antiCsrfToken = nextDetail.antiCsrfToken;
    this.log('info', '获取next_token成功');
    this.statusWindow?.update('log', '🎫 获取next_token成功');

    // 等待随机时间
    await randomDelay(1000, 2000);

    // 判断处理逻辑
    this.log('info', `判断处理逻辑: nextDetail.data=${nextDetail.data ? '有数据' : 'null'}, 风险等级=${hasProductSafetyCert}`);
    this.statusWindow?.update('log', `🔍 判断处理逻辑: nextDetail.data=${nextDetail.data ? '有数据' : 'null'}, 风险等级=${hasProductSafetyCert}`);

    if (nextDetail.data === null && hasProductSafetyCert === 2) {
      // 高危无提交按钮 - 直接跳过并记录
      this.handleHighRiskNoSubmit(defectInfo, insertData);
    } else if (nextDetail.data && nextDetail.data.artifacts && Array.isArray(nextDetail.data.artifacts) && nextDetail.data.artifacts.length === 0) {
      // 未提交的数据 - artifacts数组为空表示未提交
      await this.handleUnsubmittedData(defectInfo, insertData, defectInfo.policyEntityId, resolutionPaths);
    } else {
      // 已提交的数据 - artifacts数组有内容表示已提交
      this.handleAlreadySubmitted(defectInfo, insertData);
    }
  }

  /**
   * 处理高危无提交按钮情况
   * @param {Object} defectInfo 缺陷信息
   * @param {Object} insertData 插入数据
   */
  handleHighRiskNoSubmit(defectInfo, insertData) {
    this.log('warn', `高危无提交按钮，直接跳过: SKU: ${defectInfo.sku}, ASIN: ${defectInfo.asin}`);
    this.statusWindow?.update('action', `跳过高危项目: ${defectInfo.sku}`);
    this.statusWindow?.update('log', `🚨 高危无提交按钮，跳过: ${defectInfo.sku} (ASIN: ${defectInfo.asin})`);

    // 记录高危跳过的ASIN
    this.state.highRiskSkippedList.push({
      sku: defectInfo.sku,
      asin: defectInfo.asin,
      policyName: defectInfo.policyName
    });
    this.state.skippedCount++;

    insertData.is_click = 2;
    insertData.data_status = 2;

    // 发送数据到background script
    this.sendMessage({
      type: 'DATABASE_INSERT',
      action: 'high_risk_no_submit',
      data: insertData
    });

    this.updateStats();
  }

  /**
   * 处理未提交数据
   * @param {Object} defectInfo 缺陷信息
   * @param {Object} insertData 插入数据
   * @param {string} policyEntityId 政策实体ID
   * @param {string} resolutionPaths 解决路径
   */
  async handleUnsubmittedData(defectInfo, insertData, policyEntityId, resolutionPaths) {
    this.log('info', `检测到未提交的数据，执行提交: SKU: ${defectInfo.sku}, ASIN: ${defectInfo.asin}`);
    this.statusWindow?.update('action', `执行提交: ${defectInfo.sku}`);
    this.statusWindow?.update('log', `📤 检测到未提交数据，执行提交: ${defectInfo.sku}`);

    const submitResult = await this.submitSafetyAttestation(policyEntityId, resolutionPaths, defectInfo.sku);

    if (submitResult) {
      this.log('info', `提交成功: ${JSON.stringify(submitResult.data)}`);
      this.statusWindow?.update('log', `✅ 提交成功: ${defectInfo.sku}`);
      this.state.clickedSkuList.push(defectInfo.sku);
      this.state.submittedCount++;
      insertData.is_click = 1;

      // 发送数据到background script
      this.sendMessage({
        type: 'DATABASE_INSERT',
        action: 'submit_success',
        data: insertData,
        submitResult: submitResult.data
      });
    } else {
      this.log('error', '提交失败');
      this.statusWindow?.update('log', `❌ 提交失败: ${defectInfo.sku}`);
    }

    this.updateStats();
  }

  /**
   * 处理已提交数据
   * @param {Object} defectInfo 缺陷信息
   * @param {Object} insertData 插入数据
   */
  handleAlreadySubmitted(defectInfo, insertData) {
    this.log('info', `已提交的数据，跳过: SKU: ${defectInfo.sku}, ASIN: ${defectInfo.asin}`);
    this.statusWindow?.update('action', `跳过已提交: ${defectInfo.sku}`);
    this.statusWindow?.update('log', `✅ 已提交数据，跳过: ${defectInfo.sku}`);

    this.state.skippedCount++;
    insertData.is_click = -1;

    // 发送数据到background script
    this.sendMessage({
      type: 'DATABASE_INSERT',
      action: 'already_submitted',
      data: insertData
    });

    this.updateStats();
  }

  /**
   * 获取政策详情
   * @param {string} policyEntityId 政策实体ID
   * @returns {Promise<Object|null>}
   */
  async getPolicyDetail(policyEntityId) {
    const headers = {
      "authority": this.state.domain,
      "accept": "*/*",
      "accept-language": "zh-TW,zh;q=0.9",
      "anti-csrftoken-a2z-request": "true",
      "Content-Type": "application/json",
      "origin": `https://${this.state.domain}`,
      "referer": `https://${this.state.domain}/performance/account/health/product-policies?t=regulatory-compliance`,
      "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": '"Windows"',
      "sec-fetch-dest": "empty",
      "sec-fetch-mode": "cors",
      "sec-fetch-site": "same-origin",
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    };

    const body = { "policyEntityId": parseInt(policyEntityId, 10) };
    const url = `https://${this.state.domain}/spx/myc/myc-backend-service/api/search-scpm-policies`;

    return await this.sendApiRequest(url, 'POST', headers, body);
  }

  /**
   * 获取提交批次信息
   * @param {string} policyEntityId 政策实体ID
   * @param {string} resolutionPaths 解决路径
   * @param {string} sku SKU
   * @returns {Promise<Object|null>}
   */
  async getArtifactSubmissionBatch(policyEntityId, resolutionPaths, sku) {
    const headers = {
      "authority": this.state.domain,
      "accept": "*/*",
      "accept-language": "zh-TW,zh;q=0.9",
      "anti-csrftoken-a2z-request": "true",
      "Content-Type": "application/json",
      "origin": `https://${this.state.domain}`,
      "referer": `https://${this.state.domain}/performance/account/health/product-policies?t=regulatory-compliance`,
      "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": '"Windows"',
      "sec-fetch-dest": "empty",
      "sec-fetch-mode": "cors",
      "sec-fetch-site": "same-origin",
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    };

    const body = {
      "contributor": { "ContributorType": "SELLER" },
      "policyId": parseInt(policyEntityId, 10),
      "resolutionPathId": resolutionPaths,
      "entity": {
        "grain": "SELLING_PARTNER_SKU_MARKETPLACE",
        "values": { "SKU": sku }
      },
      "artifactRequests": [{
        "namespace": "contribution",
        "name": "gpsr_safety_attestation",
        "schemaSource": "UMP"
      }]
    };

    const url = `https://${this.state.domain}/spx/myc/myc-backend-service/api/get-artifact-submission-batch`;

    return await this.sendApiRequest(url, 'POST', headers, body);
  }

  /**
   * 提交安全认证
   * @param {string} policyEntityId 政策实体ID
   * @param {string} resolutionPaths 解决路径
   * @param {string} sku SKU
   * @returns {Promise<Object|null>}
   */
  async submitSafetyAttestation(policyEntityId, resolutionPaths, sku) {
    const headers = {
      "authority": this.state.domain,
      "accept": "*/*",
      "accept-language": "en,en-GB;q=0.9",
      "anti-csrftoken-a2z": this.state.antiCsrfToken,
      "content-type": "application/json",
      "origin": `https://${this.state.domain}`,
      "referer": `https://${this.state.domain}/performance/account/health/product-policies?t=regulatory-compliance`,
      "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": '"Windows"',
      "sec-fetch-dest": "empty",
      "sec-fetch-mode": "cors",
      "sec-fetch-site": "same-origin",
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    };

    const body = {
      "contributor": { "ContributorType": "SELLER", "ContributorValue": this.state.sellerId },
      "policyId": parseInt(policyEntityId, 10),
      "resolutionPathId": resolutionPaths,
      "entity": {
        "grain": "SELLING_PARTNER_SKU_MARKETPLACE",
        "values": { "SKU": sku }
      },
      "artifacts": [{
        "namespace": "contribution",
        "name": "gpsr_safety_attestation",
        "schemaSource": "UMP",
        "payload": '{"value":true}',
        "selectors": {}
      }]
    };

    const url = `https://${this.state.domain}/spx/myc/myc-backend-service/api/put-artifact-submission-batch`;

    return await this.sendApiRequest(url, 'POST', headers, body);
  }

  /**
   * 发送API请求的通用函数
   * @param {string} url 请求URL
   * @param {string} method 请求方法
   * @param {Object} headers 请求头
   * @param {Object} body 请求体
   * @returns {Promise<Object|null>}
   */
  async sendApiRequest(url, method, headers, body = null) {
    return new Promise((resolve) => {
      // 通过background script执行脚本注入
      this.sendMessage({
        type: 'EXECUTE_SCRIPT',
        url: url,
        method: method,
        headers: headers,
        body: body
      }).then((response) => {
        if (!response || response.error) {
          this.log('error', 'API请求错误', response?.error);
          resolve(null);
          return;
        }
        this.log('info', 'API响应成功');
        resolve(response);
      });
    });
  }

  /**
   * 点击下一页
   * @returns {Promise<boolean>}
   */
  async clickNextPage() {
    return new Promise((resolve) => {
      this.sendMessage({
        type: 'CLICK_NEXT_PAGE'
      }).then((response) => {
        resolve(response?.success || false);
      });
    });
  }

  /**
   * 等待页面加载
   * @returns {Promise<boolean>}
   */
  async waitForPageLoad() {
    return new Promise((resolve) => {
      this.sendMessage({
        type: 'WAIT_FOR_PAGE_LOAD'
      }).then((response) => {
        resolve(response?.loaded || true);
      });
    });
  }

  /**
   * 更新进度
   */
  updateProgress() {
    this.statusWindow?.update('progress', null);
  }

  /**
   * 更新统计信息
   */
  updateStats() {
    this.statusWindow?.setStats({
      processed: this.state.totalProgress,
      submitted: this.state.submittedCount,
      skipped: this.state.skippedCount,
      highRiskSkipped: this.state.highRiskSkippedList.length,
      nonWarningSkipped: this.state.nonWarningSkippedCount
    });
  }

  /**
   * 显示处理完成统计
   */
  showCompletionSummary() {
    this.log('info', '=== 处理完成统计 ===');
    this.log('info', `安全警告处理数量: ${this.state.totalProgress}`);
    this.log('info', `成功提交数量: ${this.state.submittedCount}`);
    this.log('info', `审核中跳过数量: ${this.state.skippedCount}`);
    this.log('info', `跳过非警告数据数量: ${this.state.nonWarningSkippedCount}`);
    this.log('info', `高危跳过数量: ${this.state.highRiskSkippedList.length}`);

    this.statusWindow?.update('action', '✅ 全部处理完成');
    this.statusWindow?.update('log', '=== 📊 处理完成统计 ===');
    this.statusWindow?.update('log', `📊 安全警告处理数量: ${this.state.totalProgress}`);
    this.statusWindow?.update('log', `✅ 成功提交数量: ${this.state.submittedCount}`);
    this.statusWindow?.update('log', `⏭️ 审核中跳过数量: ${this.state.skippedCount}`);
    this.statusWindow?.update('log', `⏭️ 跳过非警告数据数量: ${this.state.nonWarningSkippedCount}`);
    this.statusWindow?.update('log', `🚨 高危跳过数量: ${this.state.highRiskSkippedList.length}`);

    if (this.state.highRiskSkippedList.length > 0) {
      this.statusWindow?.update('log', '🚨 高危跳过项目详情（需要手动处理）:');
      this.state.highRiskSkippedList.forEach((item, index) => {
        this.statusWindow?.update('log', `${index + 1}. SKU: ${item.sku}, ASIN: ${item.asin}, 政策: ${item.policyName}`);
      });

      // 弹出详细的高危项目列表
      let highRiskMessage = `🚨 处理完成！发现 ${this.state.highRiskSkippedList.length} 个高危项目需要手动处理：\n\n`;
      this.state.highRiskSkippedList.forEach((item, index) => {
        highRiskMessage += `${index + 1}. SKU: ${item.sku}\n   ASIN: ${item.asin}\n   政策: ${item.policyName}\n\n`;
      });
      highRiskMessage += '请手动检查这些项目并进行相应处理。';

      alert(highRiskMessage);
    } else {
      this.statusWindow?.update('log', '🎉 没有高危项目需要手动处理');
    }

    // 发送完成消息到background script
    this.sendMessage({
      type: 'PROCESS_COMPLETE',
      message: '处理完成',
      statistics: {
        total: this.state.totalProgress,
        submitted: this.state.submittedCount,
        skipped: this.state.skippedCount,
        nonWarningSkipped: this.state.nonWarningSkippedCount,
        highRiskSkipped: this.state.highRiskSkippedList.length,
        highRiskList: this.state.highRiskSkippedList
      }
    });
  }

  /**
   * 清理资源
   */
  cleanup() {
    // 清理状态窗口
    if (this.statusWindow) {
      this.statusWindow.destroy();
      this.statusWindow = null;
    }

    // 调用父类清理方法
    super.cleanup();
  }
}

export default ProductPolicyPage;
