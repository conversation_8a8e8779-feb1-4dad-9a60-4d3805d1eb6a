import EventBus from '../core/EventBus.js';

/**
 * 状态窗口组件
 * 显示插件运行状态和进度信息
 */
class StatusWindow {
  constructor(config = {}) {
    this.config = {
      title: '🤖 Amazon 插件运行状态',
      position: { top: '20px', right: '20px' },
      width: '350px',
      maxHeight: '400px',
      ...config
    };
    
    this.eventBus = EventBus;
    this.element = null;
    this.isVisible = false;
    
    // 状态数据
    this.stats = {
      total: 0,
      processed: 0,
      submitted: 0,
      skipped: 0,
      highRiskSkipped: 0,
      nonWarningSkipped: 0
    };
    
    this.currentAction = '准备中...';
    this.logs = [];
    this.maxLogs = 50;
    
    // 绑定方法
    this.close = this.close.bind(this);
  }

  /**
   * 创建状态窗口
   */
  create() {
    if (this.element) {
      console.warn('⚠️ 状态窗口已存在');
      return;
    }

    console.log('🎨 创建状态窗口');
    
    this.element = document.createElement('div');
    this.element.id = 'amazon-plugin-status';
    this.element.innerHTML = this.generateHTML();
    
    // 添加样式
    this.addStyles();
    
    // 添加到页面
    document.body.appendChild(this.element);
    
    // 绑定事件
    this.bindEvents();
    
    this.isVisible = true;
    
    // 触发创建事件
    this.eventBus.emit('statusWindow:created', {
      windowId: this.element.id
    });
  }

  /**
   * 生成HTML内容
   * @returns {string}
   */
  generateHTML() {
    const testModeText = this.config.isTestMode ? ' (测试模式)' : '';
    
    return `
      <div style="
        position: fixed;
        top: ${this.config.position.top};
        right: ${this.config.position.right};
        width: ${this.config.width};
        max-height: ${this.config.maxHeight};
        background: #fff;
        border: 2px solid #007cba;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        font-family: Arial, sans-serif;
        font-size: 12px;
      ">
        <div style="
          background: #007cba;
          color: white;
          padding: 8px 12px;
          border-radius: 6px 6px 0 0;
          font-weight: bold;
          display: flex;
          justify-content: space-between;
          align-items: center;
        ">
          <span>${this.config.title}${testModeText}</span>
          <button id="close-status-window" style="
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
          ">×</button>
        </div>
        <div id="status-content" style="
          padding: 12px;
          max-height: 320px;
          overflow-y: auto;
        ">
          <div id="progress-info" style="margin-bottom: 10px;">
            <div style="font-weight: bold; color: #333;">📊 处理进度</div>
            <div id="progress-text">等待开始...</div>
            <div style="background: #f0f0f0; height: 6px; border-radius: 3px; margin: 5px 0;">
              <div id="progress-bar" style="background: #007cba; height: 100%; border-radius: 3px; width: 0%; transition: width 0.3s;"></div>
            </div>
          </div>
          <div id="current-action" style="margin-bottom: 10px;">
            <div style="font-weight: bold; color: #333;">🔄 当前操作</div>
            <div id="action-text">准备中...</div>
          </div>
          <div id="statistics-info" style="margin-bottom: 10px;">
            <div style="font-weight: bold; color: #333;">📈 处理统计</div>
            <div id="stats-content" style="font-size: 11px; color: #666; line-height: 1.3;">
              已处理: <span id="processed-count">0</span> |
              已提交: <span id="submitted-count">0</span> |
              审核中跳过: <span id="skipped-count">0</span> |
              高危跳过: <span id="high-risk-count">0</span> |
              跳过非警告: <span id="non-warning-skipped-count">0</span>
            </div>
          </div>
          <div id="log-section">
            <div style="font-weight: bold; color: #333;">📝 运行日志</div>
            <div id="log-content" style="
              background: #f8f8f8;
              border: 1px solid #ddd;
              border-radius: 4px;
              padding: 8px;
              max-height: 150px;
              overflow-y: auto;
              font-family: monospace;
              font-size: 11px;
              line-height: 1.4;
            ">
              插件已加载，等待开始处理...
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 添加样式
   */
  addStyles() {
    if (document.getElementById('status-window-styles')) {
      return;
    }

    const style = document.createElement('style');
    style.id = 'status-window-styles';
    style.textContent = `
      #close-status-window:hover {
        background: rgba(255,255,255,0.2) !important;
        border-radius: 50%;
      }
      
      #status-content::-webkit-scrollbar {
        width: 6px;
      }
      
      #status-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }
      
      #status-content::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }
      
      #status-content::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }
      
      #log-content::-webkit-scrollbar {
        width: 4px;
      }
      
      #log-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
      }
      
      #log-content::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    const closeButton = this.element.querySelector('#close-status-window');
    if (closeButton) {
      closeButton.addEventListener('click', this.close);
    }
  }

  /**
   * 更新状态窗口内容
   * @param {string} type 更新类型
   * @param {*} data 更新数据
   */
  update(type, data) {
    if (!this.element || !this.isVisible) {
      return;
    }

    const timestamp = new Date().toLocaleTimeString();

    switch (type) {
      case 'progress':
        this.updateProgress();
        break;
      case 'action':
        this.updateAction(data);
        break;
      case 'log':
        this.addLog(data);
        break;
      case 'total':
        this.stats.total = data;
        this.updateProgress();
        break;
      case 'stats':
        this.updateStats();
        break;
    }
  }

  /**
   * 更新进度
   */
  updateProgress() {
    const progressText = this.element.querySelector('#progress-text');
    const progressBar = this.element.querySelector('#progress-bar');
    
    if (!progressText || !progressBar) return;

    const totalProcessed = this.stats.processed + this.stats.nonWarningSkipped;
    const percentage = this.stats.total > 0 ? Math.round((totalProcessed / this.stats.total) * 100) : 0;
    
    progressText.textContent = `${totalProcessed}/${this.stats.total} (${percentage}%)`;
    progressBar.style.width = `${percentage}%`;
  }

  /**
   * 更新当前操作
   * @param {string} action 操作描述
   */
  updateAction(action) {
    const actionText = this.element.querySelector('#action-text');
    if (actionText) {
      actionText.textContent = action;
      this.currentAction = action;
    }
  }

  /**
   * 添加日志
   * @param {string} message 日志消息
   */
  addLog(message) {
    const logContent = this.element.querySelector('#log-content');
    if (!logContent) return;

    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.style.marginBottom = '2px';
    logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
    
    logContent.appendChild(logEntry);
    logContent.scrollTop = logContent.scrollHeight;

    // 限制日志条数
    this.logs.push({ timestamp, message });
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
      const firstChild = logContent.firstChild;
      if (firstChild) {
        logContent.removeChild(firstChild);
      }
    }
  }

  /**
   * 更新统计信息
   */
  updateStats() {
    const elements = {
      processed: this.element.querySelector('#processed-count'),
      submitted: this.element.querySelector('#submitted-count'),
      skipped: this.element.querySelector('#skipped-count'),
      highRisk: this.element.querySelector('#high-risk-count'),
      nonWarning: this.element.querySelector('#non-warning-skipped-count')
    };

    if (elements.processed) elements.processed.textContent = this.stats.processed;
    if (elements.submitted) elements.submitted.textContent = this.stats.submitted;
    if (elements.skipped) elements.skipped.textContent = this.stats.skipped;
    if (elements.highRisk) elements.highRisk.textContent = this.stats.highRiskSkipped;
    if (elements.nonWarning) elements.nonWarning.textContent = this.stats.nonWarningSkipped;
  }

  /**
   * 设置统计数据
   * @param {Object} stats 统计数据
   */
  setStats(stats) {
    this.stats = { ...this.stats, ...stats };
    this.updateStats();
    this.updateProgress();
  }

  /**
   * 显示窗口
   */
  show() {
    if (this.element) {
      this.element.style.display = 'block';
      this.isVisible = true;
    }
  }

  /**
   * 隐藏窗口
   */
  hide() {
    if (this.element) {
      this.element.style.display = 'none';
      this.isVisible = false;
    }
  }

  /**
   * 关闭窗口
   */
  close() {
    if (this.element) {
      this.element.remove();
      this.element = null;
      this.isVisible = false;
      
      // 触发关闭事件
      this.eventBus.emit('statusWindow:closed', {});
    }
  }

  /**
   * 销毁窗口
   */
  destroy() {
    this.close();
    
    // 移除样式
    const styles = document.getElementById('status-window-styles');
    if (styles) {
      styles.remove();
    }
  }
}

export default StatusWindow;
