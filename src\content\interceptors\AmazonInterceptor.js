import BaseInterceptor from './BaseInterceptor.js';

/**
 * Amazon API拦截器
 * 专门处理Amazon相关的API请求拦截
 */
class AmazonInterceptor extends BaseInterceptor {
  constructor(config = {}) {
    super(config);
    
    // 默认拦截的端点
    this.defaultEndpoints = [
      '/performance/api/product/policy/defects/pagination',
      '/performance/api/summary',
      'zh-CN.json'
    ];
    
    // 合并配置中的端点
    this.endpoints = [...this.defaultEndpoints, ...(config.endpoints || [])];
    
    console.log(`🔧 Amazon拦截器配置端点:`, this.endpoints);
  }

  /**
   * 检查是否应该拦截此URL
   * @param {string} url 请求URL
   * @returns {boolean}
   */
  shouldIntercept(url) {
    return this.endpoints.some(endpoint => url.includes(endpoint));
  }

  /**
   * 处理拦截到的响应
   * @param {string} url 请求URL
   * @param {Response} response 响应对象
   * @param {Array} requestArgs 原始请求参数
   */
  async handleResponse(url, response, requestArgs) {
    // 解析JSON响应
    const jsonData = await this.parseJsonResponse(response);
    
    if (!jsonData) {
      console.warn(`⚠️ 无法解析响应数据: ${url}`);
      return;
    }

    // 根据URL类型处理不同的响应
    if (url.includes('/performance/api/product/policy/defects/pagination') && url.includes('statuses=Open')) {
      this.handleDefectsPagination(url, jsonData);
    } else if (url.includes('/performance/api/summary')) {
      this.handleSummary(url, jsonData);
    } else if (url.includes('zh-CN.json')) {
      this.handleLocalization(url, jsonData);
    } else if (url.includes('/revenuecalculator/getbatchfeesamount')) {
      this.handleRevenueFees(url, jsonData);
    } else {
      this.handleGeneric(url, jsonData);
    }
  }

  /**
   * 处理缺陷分页数据
   * @param {string} url 请求URL
   * @param {Object} data 响应数据
   */
  handleDefectsPagination(url, data) {
    console.log('🔍 处理缺陷分页数据:', url);
    
    // 提取关键信息
    const defects = data.defects || [];
    const sellerId = data.sellerId;
    
    console.log(`📊 缺陷数据: ${defects.length}条, sellerId: ${sellerId}`);
    
    // 发送拦截数据
    this.emitInterceptedData('defects-pagination', url, data);
    
    // 触发特定事件
    this.eventBus.emit('amazon:defects-pagination', {
      url,
      data,
      defectsCount: defects.length,
      sellerId
    });
  }

  /**
   * 处理摘要数据
   * @param {string} url 请求URL
   * @param {Object} data 响应数据
   */
  handleSummary(url, data) {
    console.log('📈 处理摘要数据:', url);
    
    // 提取总数据量
    const totalNum = data?.listingLevelMetrics?.REGULATORY_COMPLIANCE?.defects?.count || 0;
    
    console.log(`📊 总数据量: ${totalNum}`);
    
    // 发送拦截数据
    this.emitInterceptedData('summary', url, data);
    
    // 触发特定事件
    this.eventBus.emit('amazon:summary', {
      url,
      data,
      totalCount: totalNum
    });
  }

  /**
   * 处理本地化数据
   * @param {string} url 请求URL
   * @param {Object} data 响应数据
   */
  handleLocalization(url, data) {
    console.log('🌐 处理本地化数据:', url);
    
    // 发送拦截数据
    this.emitInterceptedData('localization', url, data);
    
    // 触发特定事件
    this.eventBus.emit('amazon:localization', {
      url,
      data
    });
  }

  /**
   * 处理收入计算器费用数据
   * @param {string} url 请求URL
   * @param {Object} data 响应数据
   */
  handleRevenueFees(url, data) {
    console.log('💰 处理收入计算器费用数据:', url);

    // 提取费用列表信息
    const feesAmountRequestList = data?.feesAmountRequestList || [];

    console.log(`💸 费用数据: ${feesAmountRequestList.length}条`);

    // 发送拦截数据
    this.emitInterceptedData('revenue-fees', url, data);

    // 触发特定事件
    this.eventBus.emit('amazon:revenue-fees', {
      url,
      data,
      feesCount: feesAmountRequestList.length
    });
  }

  /**
   * 处理通用数据
   * @param {string} url 请求URL
   * @param {Object} data 响应数据
   */
  handleGeneric(url, data) {
    console.log('❓ 处理未知类型数据:', url);

    // 发送拦截数据
    this.emitInterceptedData('generic', url, data);

    // 触发特定事件
    this.eventBus.emit('amazon:generic', {
      url,
      data
    });
  }

  /**
   * 添加新的拦截端点
   * @param {string} endpoint 端点路径
   */
  addEndpoint(endpoint) {
    if (!this.endpoints.includes(endpoint)) {
      this.endpoints.push(endpoint);
      console.log(`➕ 添加拦截端点: ${endpoint}`);
    }
  }

  /**
   * 移除拦截端点
   * @param {string} endpoint 端点路径
   */
  removeEndpoint(endpoint) {
    const index = this.endpoints.indexOf(endpoint);
    if (index > -1) {
      this.endpoints.splice(index, 1);
      console.log(`➖ 移除拦截端点: ${endpoint}`);
    }
  }

  /**
   * 获取当前拦截的端点列表
   * @returns {Array<string>}
   */
  getEndpoints() {
    return [...this.endpoints];
  }
}

export default AmazonInterceptor;
