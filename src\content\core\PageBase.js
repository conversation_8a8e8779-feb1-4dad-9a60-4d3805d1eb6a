import EventBus from './EventBus.js';

/**
 * 页面处理基类
 * 所有页面处理器都应该继承此类
 */
class PageBase {
  constructor(config = {}) {
    this.config = config;
    this.interceptors = [];
    this.processors = [];
    this.uiComponents = [];
    this.isActive = false;
    this.eventBus = EventBus;
    
    // 绑定方法到实例
    this.handleMessage = this.handleMessage.bind(this);
    this.cleanup = this.cleanup.bind(this);
  }

  /**
   * 检查当前页面是否应该激活此处理器
   * 子类必须实现此方法
   * @returns {boolean}
   */
  shouldActivate() {
    throw new Error('PageBase.shouldActivate() must be implemented by subclass');
  }

  /**
   * 获取页面配置信息
   * 子类可以重写此方法
   * @returns {Object}
   */
  getPageConfig() {
    return {
      name: this.constructor.name,
      url: window.location.href,
      domain: window.location.hostname,
      path: window.location.pathname
    };
  }

  /**
   * 初始化页面处理器
   */
  async initialize() {
    if (!this.shouldActivate()) {
      console.log(`🚫 ${this.constructor.name} 不在当前页面激活`);
      return false;
    }

    console.log(`🚀 ${this.constructor.name} 开始初始化...`);
    
    try {
      // 设置拦截器
      await this.setupInterceptors();
      
      // 设置数据处理器
      await this.setupProcessors();
      
      // 设置UI组件
      await this.setupUI();
      
      // 设置事件监听
      this.setupEventListeners();
      
      this.isActive = true;
      console.log(`✅ ${this.constructor.name} 初始化完成`);
      
      // 触发初始化完成事件
      this.eventBus.emit('page:initialized', {
        page: this.constructor.name,
        config: this.getPageConfig()
      });
      
      return true;
    } catch (error) {
      console.error(`❌ ${this.constructor.name} 初始化失败:`, error);
      return false;
    }
  }

  /**
   * 设置拦截器
   * 子类应该重写此方法
   */
  async setupInterceptors() {
    // 默认实现为空，子类可以重写
  }

  /**
   * 设置数据处理器
   * 子类应该重写此方法
   */
  async setupProcessors() {
    // 默认实现为空，子类可以重写
  }

  /**
   * 设置UI组件
   * 子类应该重写此方法
   */
  async setupUI() {
    // 默认实现为空，子类可以重写
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 监听窗口消息
    window.addEventListener('message', this.handleMessage);
    
    // 监听页面卸载
    window.addEventListener('beforeunload', this.cleanup);
  }

  /**
   * 处理窗口消息
   * 子类可以重写此方法
   * @param {MessageEvent} event
   */
  handleMessage(event) {
    // 默认实现为空，子类可以重写
  }

  /**
   * 清理资源
   */
  cleanup() {
    console.log(`🧹 ${this.constructor.name} 开始清理资源...`);
    
    // 清理拦截器
    this.interceptors.forEach(interceptor => {
      if (interceptor.deactivate) {
        interceptor.deactivate();
      }
    });
    
    // 清理UI组件
    this.uiComponents.forEach(component => {
      if (component.destroy) {
        component.destroy();
      }
    });
    
    // 移除事件监听器
    window.removeEventListener('message', this.handleMessage);
    window.removeEventListener('beforeunload', this.cleanup);
    
    this.isActive = false;
    
    // 触发清理完成事件
    this.eventBus.emit('page:cleanup', {
      page: this.constructor.name
    });
    
    console.log(`✅ ${this.constructor.name} 清理完成`);
  }

  /**
   * 发送消息到background script
   * @param {Object} message 消息对象
   * @returns {Promise}
   */
  sendMessage(message) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          console.error('❌ 发送消息失败:', chrome.runtime.lastError);
          resolve(null);
          return;
        }
        resolve(response);
      });
    });
  }

  /**
   * 记录日志
   * @param {string} level 日志级别
   * @param {string} message 日志消息
   * @param {*} data 附加数据
   */
  log(level, message, data = null) {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] [${this.constructor.name}] ${message}`;
    
    switch (level) {
      case 'error':
        console.error(logMessage, data);
        break;
      case 'warn':
        console.warn(logMessage, data);
        break;
      case 'info':
        console.info(logMessage, data);
        break;
      default:
        console.log(logMessage, data);
    }
    
    // 触发日志事件
    this.eventBus.emit('page:log', {
      page: this.constructor.name,
      level,
      message,
      data,
      timestamp
    });
  }
}

export default PageBase;
